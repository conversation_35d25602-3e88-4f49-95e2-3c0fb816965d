<ng-container *ngIf="!isUserAdultChoice && articleResponse?.isAdultsOnly; else articleContent" class="article-page">
  <app-adult (isUserAdult)="onIsUserAdultChoose($event)"></app-adult>
</ng-container>

<ng-template #articleContent>
  <div *ngIf="isOlympics2024 && this.categorySlug === 'sport'" class="olympics-header">
    <app-olympics-header></app-olympics-header>
  </div>
  <div *ngIf="isEB2024 && isEBEnabled" class="eb-header">
    <div class="eb-header-container">
      <app-eb-header-logo></app-eb-header-logo>
    </div>
  </div>
  <ng-container *ngIf="!isSorozatveto && !isMinuteToMinute && (isOpinion || isInterview)">
    @if (categorySlug === 'ahol-a-labda-lesz') {
      <mno-opinion-card
        [data]="articleCard"
        [isMobile]="isMobile$ | async"
        [styleID]="OpinionCardType.WhereTheBallWillBe"
        class="full-width"
      ></mno-opinion-card>
    } @else {
      <mno-opinion-card
        [data]="articleCard"
        [isMobile]="isMobile$ | async"
        [styleID]="isInterview ? OpinionCardType.InterviewHeader : OpinionCardType.OpinionHeader"
      ></mno-opinion-card>
    }
  </ng-container>

  <ng-container *ngIf="isMinuteToMinute">
    <mno-opinion-card
      (minuteBlockClick)="onMinuteBlockClick($event)"
      [data]="articleCard"
      [isMobile]="isMobile$ | async"
      [styleID]="OpinionCardType.MinuteToMinuteHeader"
    ></mno-opinion-card>
  </ng-container>

  <ng-container *ngIf="useTopBanner">
    <kesma-advertisement-adocean
      [isExceptionAdvertEnabled]="isExceptionAdvertEnabled"
      *ngIf="adverts?.desktop?.['roadblock_1'] as ad"
      [ad]="ad"
    ></kesma-advertisement-adocean>
    <kesma-advertisement-adocean
      [isExceptionAdvertEnabled]="isExceptionAdvertEnabled"
      *ngIf="adverts?.mobile?.['mobilrectangle_1'] as ad"
      [ad]="ad"
    ></kesma-advertisement-adocean>
  </ng-container>

  <app-article-header
    *ngIf="isSponsored && !isOpinion && !isInterview && !isMinuteToMinute && !isVideo"
    [article]="articleResponse"
    [foundationTagSlug]="foundationTagSlug"
    [foundationTagTitle]="foundationTagTitle"
    [isMobile]="isMobile$ | async"
    [languages]="articleLanguages"
    [sponsorship]="sponsorship"
    [displayCover]="canDisplayCover"
    [url]="url"
    [roadblock_1_top]="adverts?.desktop?.['roadblock_1']"
    [medium_rectangle_1_top]="adverts?.mobile?.['mobilrectangle_1']"
    [isExceptionAdvertEnabled]="isExceptionAdvertEnabled"
  ></app-article-header>

  <section
    *ngIf="id"
    [class.eb-body]="isEB2024 && isEBEnabled"
    [class.is-opinion]="isOpinion"
    [class.is-sorozatveto]="isSorozatveto"
    class="block article-block"
  >
    <ng-container *ngIf="isGallery">
      <div class="wrapper">
        <app-article-header
          *ngIf="(isGallery || isSponsored) && !isOpinion && !isInterview && !isMinuteToMinute && !isVideo"
          [article]="articleResponse"
          [displayCover]="false"
          [foundationTagSlug]="foundationTagSlug"
          [foundationTagTitle]="foundationTagTitle"
          [isMobile]="isMobile$ | async"
          [languages]="articleLanguages"
          [sponsorship]="sponsorship"
          [url]="url"
          [roadblock_1_top]="adverts?.desktop?.['roadblock_1']"
          [medium_rectangle_1_top]="adverts?.mobile?.['mobilrectangle_1']"
          [isExceptionAdvertEnabled]="isExceptionAdvertEnabled"
        ></app-article-header>
      </div>
      <div class="wrapper page flex-column">
        <ng-container *ngIf="highlightedGalleryData as galleryData">
          <mno-slider-gallery
            (navigateToGallery)="navigateToGallery($event, highlightedGalleryData)"
            [data]="highlightedGalleryData"
            [isInsideAdultArticleBody]="articleResponse?.isAdultsOnly"
            [isMobile]="isMobile$ | async"
          ></mno-slider-gallery>
        </ng-container>
        <kesma-advertisement-adocean *ngIf="adverts?.desktop?.['roadblock_1'] as ad" [ad]="ad" class="fullwidth-ad-zone"></kesma-advertisement-adocean>
        <kesma-advertisement-adocean *ngIf="adverts?.mobile?.['mobilrectangle_1'] as ad" [ad]="ad" class="fullwidth-ad-zone"></kesma-advertisement-adocean>
      </div>
    </ng-container>

    <div *ngIf="isVideo && !isSorozatveto && !isMinuteToMinute" class="wrapper">
      <div class="d-flex flex-column w-full">
        <app-article-header
          [article]="articleResponse"
          [displayCover]="false"
          [foundationTagSlug]="foundationTagSlug"
          [foundationTagTitle]="foundationTagTitle"
          [isMobile]="isMobile$ | async"
          [languages]="articleLanguages"
          [url]="url"
          [roadblock_1_top]="adverts?.desktop?.['roadblock_1']"
          [medium_rectangle_1_top]="adverts?.mobile?.['mobilrectangle_1']"
          [isExceptionAdvertEnabled]="isExceptionAdvertEnabled"
        ></app-article-header>
        <kesma-article-video
          [coverImgThumbnail]="videoLead?.coverImageUrl ?? ''"
          [data]="videoLead"
          [useCoverThumbnail]="!!videoLead?.coverImageUrl"
        ></kesma-article-video>
        <kesma-advertisement-adocean *ngIf="adverts?.desktop?.['roadblock_1'] as ad" [ad]="ad" class="fullwidth-ad-zone"></kesma-advertisement-adocean>
        <kesma-advertisement-adocean *ngIf="adverts?.mobile?.['mobilrectangle_1'] as ad" [ad]="ad" class="fullwidth-ad-zone"></kesma-advertisement-adocean>
      </div>
    </div>

    <div class="wrapper" [class.w-100]="articleResponse?.isAlternativeView">
      <div class="article d-flex flex-column">
        <app-article-header
          *ngIf="!isGallery && !isOpinion && !isInterview && !isVideo && !isSorozatveto && !isMinuteToMinute && !isSponsored"
          [article]="articleResponse"
          [foundationTagSlug]="foundationTagSlug"
          [foundationTagTitle]="foundationTagTitle"
          [isMobile]="isMobile$ | async"
          [languages]="articleLanguages"
          [displayCover]="canDisplayCover"
          [url]="url"
          [roadblock_1_top]="adverts?.desktop?.['roadblock_1']"
          [medium_rectangle_1_top]="adverts?.mobile?.['mobilrectangle_1']"
          [isExceptionAdvertEnabled]="isExceptionAdvertEnabled"
        ></app-article-header>
        <mno-opinion-card
          *ngIf="isSorozatveto"
          class="sorozatveto-highlight"
          [data]="articleCard"
          [isMobile]="isMobile$ | async"
          [styleID]="OpinionCardType.SorozatvetoHeader"
        ></mno-opinion-card>

        <ng-container *ngIf="!isSorozatveto && !isMinuteToMinute && !isSponsored">
          <kesma-advertisement-adocean *ngIf="adverts?.desktop?.['szponzorcsik'] as ad" [ad]="ad"></kesma-advertisement-adocean>
          <kesma-advertisement-adocean *ngIf="adverts?.mobile?.['szponzorcsik'] as ad" [ad]="ad"></kesma-advertisement-adocean>

          <ng-container *ngIf="!isGallery && !useTopBanner && !isVideo">
            <kesma-advertisement-adocean *ngIf="adverts?.desktop?.['roadblock_1'] as ad" [ad]="ad"></kesma-advertisement-adocean>
            <kesma-advertisement-adocean *ngIf="adverts?.mobile?.['mobilrectangle_1'] as ad" [ad]="ad"></kesma-advertisement-adocean>
          </ng-container>

          <ng-container *ngIf="!isOpinion">
            <mno-opinion-newsletter-box
              (subscribeClicked)="onSubscriptionClick()"
              [infoLink]="opinionInfoLink"
              [styleId]="OpinionNewsletterBoxType.Inline"
            ></mno-opinion-newsletter-box>
          </ng-container>
        </ng-container>

        <ng-container
          *ngIf="!reviewOpinion(); else reviewOpinionContent"
          [ngTemplateOutletContext]="{ body: body?.[((currentPage$ | async) ?? 1) - 1] }"
          [ngTemplateOutlet]="bodyContent"
        ></ng-container>

        <kesma-eb-navigator *ngIf="isEBEnabled" [styleID]="EBPortalEnum.MNO"></kesma-eb-navigator>
        <kesma-olimpia-navigator *ngIf="isOlympics2024 && !hideOnMobileApp" [styleID]="OlimpicPortalEnum.OlimpicMNO"></kesma-olimpia-navigator>

        <mno-pager
          *ngIf="!reviewOpinion() && isPagination && (currentPage$ | async) as page"
          [allowAutoScrollToTop]="true"
          [hasSkipButton]="true"
          [isListPager]="true"
          [maxDisplayedPages]="5"
          [rowAllCount]="body.length"
          [rowOnPageCount]="1"
        ></mno-pager>
        <div class="eb-mobile">
          <ng-container [ngTemplateOutlet]="ebImportantSidebar"></ng-container>
        </div>

        <ng-container *ngIf="isSorozatveto">
          <ng-container *ngTemplateOutlet="sorozatveto"></ng-container>
        </ng-container>
        <ng-container *ngIf="minuteToMinuteBlocks?.length">
          <div class="minute-to-minute-top">
            Cikkünk folyamatosan frissül <span class="minute-top-counter">{{ minuteToMinuteBlocks.length }} bejegyzés</span>
          </div>
          <div class="minute-to-minute-container">
            <ng-container *ngFor="let minuteToMinuteBlock of minuteToMinuteBlocks; let i = index">
              <div class="minute-to-minute-block">
                <div class="minute-to-minute-header">
                  <a #minuteBlock [attr.id]="'minute-block-' + i" aria-label="Percről percre"></a>
                  <p class="minute-to-minute-date">
                    {{ minuteToMinuteBlock.date | publishDate }}
                    frissült {{ minuteToMinuteBlock.date | formatDate: 'h-m' }}
                  </p>
                  <h4 class="minute-to-minute-title">{{ minuteToMinuteBlock.title }}</h4>
                </div>
                <div class="minute-to-minute-body">
                  <ng-container [ngTemplateOutletContext]="{ body: minuteToMinuteBlock.body }" [ngTemplateOutlet]="bodyContent"></ng-container>
                  <mno-social-share [isMobile]="isMobile$ | async" [link]="[url]" [title]="articleResponse?.title" color="dark"></mno-social-share>
                </div>
              </div>
              <mno-promo-block *ngIf="i === 0" [isWide]="true" style="margin-top: 32px"></mno-promo-block>
            </ng-container>
            <kesma-google-news [data]="googleNews"></kesma-google-news>
          </div>
        </ng-container>
        <ng-container *ngIf="dossier && !sponsoredTag">
          <mno-dossier-card [articlesCount]="dossierCount" [data]="dossier" [styleID]="DossierCardTypes.recommendationDossier"></mno-dossier-card>
        </ng-container>

        @if (sponsoredTag) {
          <app-sponsored-tag-box [sponsoredTag]="sponsoredTag" [excludedSlug]="articleSlug" />
        }
        <div #dataTrigger *ngIf="articleResponse"></div>

        <ng-container *ngIf="!isSorozatveto; else sorozatvetoRecommenders">
          <ng-container *ngTemplateOutlet="legolvasottabb"></ng-container>
        </ng-container>

        <ng-container *ngIf="isOpinion">
          <ng-container *ngTemplateOutlet="velemenyvaroRecommenders"></ng-container>
          <ng-container *ngTemplateOutlet="authorArticlesRecommenders"></ng-container>
        </ng-container>
        <ng-container *ngIf="isGallery">
          <ng-container *ngTemplateOutlet="galleryRecommenders"></ng-container>
        </ng-container>
        <mno-promo-block
          (subscriptionClick)="onSubscribe()"
          [facebookLink]="socialInfo.facebookLink"
          [instagramLink]="socialInfo.instagramLink"
          [isWide]="true"
          [twitterLink]="socialInfo.twitterLink"
          [videaLink]="socialInfo.videaLink"
          [youtubeLink]="socialInfo.youtubeLink"
        ></mno-promo-block>
        <kesma-google-news [data]="googleNews"></kesma-google-news>
        <app-nativterelo></app-nativterelo>

        <app-external-recommendations
          [roadblock_ottboxextra]="adverts?.desktop?.['roadblock_ottboxextra']"
          [mobilrectangle_ottboxextra]="adverts?.mobile?.['mobilrectangle_ottboxextra']"
          [isFoundation]="!!foundationTagSlug"
        ></app-external-recommendations>

        <ng-container *ngIf="electionsService.isElections2024Enabled()">
          <kesma-elections-box
            [desktopWidth]="foundationTagSlug ? 12 : 9"
            [link]="electionsService.getElections2024Link()"
            [styleID]="ElectionsBoxStyle.DIVERTER"
          ></kesma-elections-box>
        </ng-container>

        <kesma-advertisement-adocean *ngIf="adverts?.desktop?.['roadblock_2'] as ad" [ad]="ad"></kesma-advertisement-adocean>
        <kesma-advertisement-adocean *ngIf="adverts?.mobile?.['mobilrectangle_2'] as ad" [ad]="ad"></kesma-advertisement-adocean>

        <ng-container *ngTemplateOutlet="cimoldalrolAjanljuk"></ng-container>

        <kesma-advertisement-adocean *ngIf="adverts?.desktop?.['roadblock_4'] as ad" [ad]="ad"></kesma-advertisement-adocean>
        <kesma-advertisement-adocean *ngIf="adverts?.mobile?.['medium_rectangle_4_bottom'] as ad" [ad]="ad"></kesma-advertisement-adocean>
      </div>

      <div *ngIf="!foundationTagSlug && !articleResponse?.isAlternativeView" class="sidebar">
        <div class="eb-desktop">
          <ng-container [ngTemplateOutlet]="ebImportantSidebar"></ng-container>
        </div>
        <app-sidebar [adPageType]="adPageType" [articleId]="id" [articleSlug]="articleSlug" [categorySlug]="categorySlug"></app-sidebar>
      </div>
    </div>
  </section>
  <app-newsletter-popup></app-newsletter-popup>
</ng-template>

<ng-template #bodyContent let-body="body">
  <ng-container *ngFor="let element of body; let i = index; let isFirst = first">
    <ng-container [ngSwitch]="element.type">
      <ng-container *ngSwitchCase="'ContentPage.Article'">
        <ng-container *ngIf="getArticleRecommendation(element) | async as recommenderData">
          <article [attr.data-article-id]="recommenderData?.id" class="article-recommender">
            <h4 class="article-recommender-title">További {{ articleCard.category?.name }} híreink</h4>
            <ul class="list-plus-darkblue">
              <li>
                <a
                  [routerLink]="[
                    '/',
                    recommenderData?.category?.slug,
                    recommendationArticlePublishYear,
                    recommendationArticlePublishMonth,
                    recommenderData?.slug,
                  ]"
                  >{{ recommenderData.title }}</a
                >
                <img *ngIf="element?.details[0]?.value?.status?.isAdultsOnly" [size]="16" alt="18+ ikon" mnoBadge mno-icon type="adult" />
                <div class="icon-wrapper">
                  <i *ngIf="element?.details[0]?.value?.dbcache?.isVideoType" [size]="16" mnoBadge mno-icon type="video"> </i>
                  <i *ngIf="element?.details[0]?.value?.dbcache?.isPodcastType" [size]="16" mnoBadge mno-icon type="podcast"> </i>
                  <i *ngIf="element?.details[0]?.value?.dbcache?.hasGallery" [size]="16" mnoBadge mno-icon type="gallery"> </i>
                </div>
              </li>
            </ul>
          </article>
        </ng-container>
      </ng-container>
      <app-article-text
        *ngSwitchCase="'Basic.Wysiwyg.Wysiwyg'"
        [class.is-first]="isFirst"
        [data]="element"
        [hasInitial]="isOpinion && isFirst"
      ></app-article-text>
      <ng-container *ngSwitchCase="ArticleBodyType.SportCompetitionSchedules">
        <kesma-eb-results *ngIf="schedules?.[element.id] as data" [data]="data" [styleID]="EBPortalEnum.MNO_RESULTS"></kesma-eb-results>
      </ng-container>
      <ng-container *ngSwitchCase="ArticleBodyType.Quiz">
        <app-quiz [data]="element?.details[0]?.value"></app-quiz>
      </ng-container>
      <ng-container *ngSwitchCase="ArticleBodyType.Voting">
        @if (voteCache[element?.details?.[0]?.value?.id ?? ''] | async; as voteData) {
          <mno-voting (vote)="onVotingSubmit($event, voteData)" [data]="voteData.data" [voteId]="voteData.votedId" />
        }
      </ng-container>
      <mno-dossier-card
        *ngSwitchCase="ArticleBodyType.SubsequentDossier"
        [data]="element"
        [styleID]="DossierCardTypes.recommendationDossier"
      ></mno-dossier-card>
      <app-info-block *ngSwitchCase="ArticleBodyType.Infobox" [data]="element"></app-info-block>
      <ng-container *ngSwitchCase="ArticleBodyType.Gallery">
        <ng-container *ngIf="getGallery(element) as galleryData">
          <mno-slider-gallery
            (navigateToGallery)="navigateToGallery($event, galleryData)"
            [data]="galleryData"
            [isInsideAdultArticleBody]="articleResponse?.isAdultsOnly"
            [isMobile]="isMobile$ | async"
          >
          </mno-slider-gallery>
        </ng-container>
      </ng-container>
      <div *ngSwitchCase="ArticleBodyType.MediaVideo" class="block-video">
        <kesma-article-video [data]="element?.details[0]?.value"></kesma-article-video>
      </div>

      <ng-container *ngIf="(isOpinion || isSponsored) && !isMinuteToMinute && isFirst">
        <mno-opinion-newsletter-box
          (subscribeClicked)="onSubscriptionClick()"
          [infoLink]="opinionInfoLink"
          [styleId]="OpinionNewsletterBoxType.Inline"
        ></mno-opinion-newsletter-box>
      </ng-container>
      <ng-container *ngSwitchCase="ArticleBodyType.Advert">
        <kesma-advertisement-adocean *ngIf="adverts?.mobile?.[element.adverts.mobile] as ad" [ad]="ad"></kesma-advertisement-adocean>
        <kesma-advertisement-adocean *ngIf="adverts?.desktop?.[element.adverts.desktop] as ad" [ad]="ad"> </kesma-advertisement-adocean>
      </ng-container>
    </ng-container>
  </ng-container>
</ng-template>

<ng-template #sorozatveto>
  <mno-opinion-newsletter-box
    (subscribeClicked)="onSubscriptionClick()"
    [infoLink]="opinionInfoLink"
    [styleId]="OpinionNewsletterBoxType.Inline"
  ></mno-opinion-newsletter-box>
  <mno-block-title-row [data]="{ text: 'Összes idézet' }" />
  <div class="sorozatveto-main-opinions">
    <div *ngFor="let review of sorozatvetoArticle.reviews" class="sorozatveto-opinion-card">
      <a [href]="getReviewLink(review)" target="_blank" class="sorozatveto-opinion-card-link" aria-label="Link"></a>
      <h4 class="sorozatveto-opinion-card-header">
        <mno-opinion-author [data]="review.author" size="medium" [hideAvatar]="true" />
      </h4>
      <div class="sorozatveto-opinion-card-lead">
        <span class="lead">{{ review.lead }}</span>
      </div>
    </div>
  </div>
  <div class="sorozatveto-authors"></div>
</ng-template>

<ng-template #cimoldalrolAjanljuk>
  <app-recommendation-block
    *ngIf="recommendedArticles?.length && !foundationTagSlug"
    [articles]="recommendedArticles"
    [desktopStyle]="ArticleCardType.Img4TopTagsTitleLeadSmallBorder"
    [isMobile]="isMobile$ | async"
    [mobileStyle]="ArticleCardType.Img4TopTagsTitleLeadSmallBorder"
    [title]="lowPriorityRecommendationTitle"
  >
  </app-recommendation-block>

  <app-foundation-recommendation
    *ngIf="foundationTagSlug && articleSlug"
    [articleSlug]="articleSlug"
    [foundationTagSlug]="foundationTagSlug"
    [tags]="tags"
  ></app-foundation-recommendation>

  <kesma-advertisement-adocean *ngIf="adverts?.desktop?.['roadblock_3'] as ad" [ad]="ad"></kesma-advertisement-adocean>
  <kesma-advertisement-adocean *ngIf="adverts?.mobile?.['mobilrectangle_3'] as ad" [ad]="ad"></kesma-advertisement-adocean>
</ng-template>

<ng-template #legolvasottabb>
  <app-recommendation-block
    *ngIf="recommendedHighPriorityArticles?.length"
    [articles]="recommendedHighPriorityArticles"
    [desktopStyle]="ArticleCardType.Img4TopTagsTitleLeadSmallBorder"
    [isMobile]="isMobile$ | async"
    [mobileStyle]="ArticleCardType.Img4TopTagsTitleLeadSmallBorder"
    [title]="highPriorityRecommendationTitle"
  >
  </app-recommendation-block>
</ng-template>

<ng-template #sorozatvetoRecommenders>
  <mno-block-title-row *ngIf="sorozatvetoArticles?.length" [data]="sorozatvetoTitle" [styleID]="BlockTitleRowType.Opinion"></mno-block-title-row>

  <div class="recommendation">
    <ng-container *ngFor="let articleCard of sorozatvetoArticles | slice: 0 : 4">
      <mno-opinion-card [data]="articleCard" [isMobile]="isMobile$ | async" [styleID]="OpinionCardType.LabelTitleBadge"></mno-opinion-card>
    </ng-container>
  </div>

  <ng-container *ngTemplateOutlet="velemenyvaroRecommenders"></ng-container>
</ng-template>

<ng-template #velemenyvaroRecommenders>
  <mno-block-title-row *ngIf="velemenyvaroArticles?.length" [data]="velemenyvaroTitle" [styleID]="BlockTitleRowType.Opinion"></mno-block-title-row>

  <div class="recommendation">
    <ng-container *ngFor="let articleCard of velemenyvaroArticles | slice: 0 : 4">
      <mno-opinion-card [data]="articleCard" [isMobile]="isMobile$ | async" [styleID]="OpinionCardType.AuthorLabelTitleBadge"></mno-opinion-card>
    </ng-container>
  </div>
</ng-template>

<ng-template #authorArticlesRecommenders>
  <ng-container *ngIf="authorArticles$ | async as authorArticles">
    <mno-block-title-row [data]="authorArticlesTitle" [styleID]="BlockTitleRowType.Opinion"></mno-block-title-row>

    <div class="recommendation">
      <ng-container *ngFor="let articleCard of authorArticles">
        <article [data]="articleCard" [styleID]="ArticleCardType.Img4TopTagsTitleLeadSmallBorder" mno-article-card></article>
      </ng-container>
    </div>
  </ng-container>
</ng-template>

<ng-template #galleryRecommenders>
  <ng-container *ngIf="galleryArticles$ | async as galleryArticles">
    <mno-block-title-row [data]="galleryArticlesTitle"></mno-block-title-row>

    <div class="recommendation">
      <ng-container *ngFor="let articleCard of galleryArticles">
        <article
          [asGallery]="true"
          [data]="articleCard"
          [isInsideAdultArticleBody]="articleResponse?.isAdultsOnly"
          [styleID]="ArticleCardType.Gallery"
          mno-article-card
        ></article>
      </ng-container>
    </div>
  </ng-container>
</ng-template>

<ng-template #ebImportantSidebar>
  <app-layout
    *ngIf="ebImportantSidebarLayout$ | async as layout"
    [adPageType]="adPageType"
    [configuration]="layout.content"
    [layoutType]="LayoutPageType.SIDEBAR"
    [structure]="layout.struct"
    class="eb-sidebar"
  ></app-layout>
</ng-template>

<ng-template #reviewOpinionContent>
  <h3 class="sorozatveto-opinion-card-header">
    <mno-opinion-author [data]="reviewOpinion()!.author" size="medium" />
  </h3>
  <div class="sorozatveto-opinion-card-content">
    @if (reviewBlockIsArray(reviewOpinion()!)) {
      <mno-wysiwyg-box [htmlArray]="getReviewBody(reviewOpinion()!)" />
    } @else {
      <mno-wysiwyg-box [html]="getReviewBlock(reviewOpinion()!)" />
    }
  </div>
</ng-template>
