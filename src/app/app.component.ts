import { DOCUMENT } from '@angular/common';
import { ChangeDetectionStrategy, Component, Inject, OnDestroy, OnInit, Optional } from '@angular/core';
import { ActivatedRoute, ChildActivationEnd, Data, NavigationEnd, Router, RouterOutlet } from '@angular/router';
import { REQUEST, SchemaOrgService, SeoService, StorageService, UtilService } from '@trendency/kesma-core';
import { AnalyticsService } from '@trendency/kesma-ui';
import { GoogleTagManagerService } from 'angular-google-tag-manager';
import { Observable, Subject, takeUntil } from 'rxjs';
import { buffer, filter, map, tap } from 'rxjs/operators';
import { environment } from 'src/environments/environment';
import { ContentLanguageService, defaultMetaInfo, MOBILE_APP_DOM_REMOVE_DATA_KEY, SchemaOrgWebpageDataTemplate, UrlService } from './shared';
import { ScrollPositionService } from './shared/services/scroll-position.service';

const ADVERTICUM_COOKIE = '_goa3GDPR';
const ADVERTICUM_COOKIE_CLEAN_KEY = 'cookie-cleanup-adverticum';

@Component({
  selector: 'app-root',
  template: '<router-outlet></router-outlet>',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [RouterOutlet],
})
export class AppComponent implements OnInit, OnDestroy {
  private isFirstNavigation = true;
  private readonly unsubscribe = new Subject<void>();

  constructor(
    private readonly seoService: SeoService,
    private readonly utilsService: UtilService,
    private readonly schemaService: SchemaOrgService,
    private readonly router: Router,
    private readonly urlService: UrlService,
    private readonly analyticsService: AnalyticsService,
    private readonly gtmService: GoogleTagManagerService,
    private readonly storageService: StorageService,
    private readonly route: ActivatedRoute,
    private readonly contentLanguageService: ContentLanguageService,
    private readonly scrollPositionService: ScrollPositionService,
    @Inject(DOCUMENT) private readonly document: Document,
    @Optional() @Inject(REQUEST) private readonly req: Request
  ) {}

  ngOnInit(): void {
    this.cleanup();
    this.setupMobileAppParameters();
    this.seoService.setMetaData(defaultMetaInfo, { skipSeoMetaCheck: true });
    SchemaOrgWebpageDataTemplate.url = this.seoService.hostUrl;
    this.schemaService.insertSchema(SchemaOrgWebpageDataTemplate);
    if (this.utilsService.isBrowser()) {
      this.setupAnalyticsTracking();
      this.gtmService.addGtmToDom();
      this.scrollPositionService.setupScrollPositionListener();
    }

    (this.router.events.pipe(filter((event) => event instanceof NavigationEnd)) as Observable<NavigationEnd>).subscribe((event: NavigationEnd): void => {
      this.contentLanguageService.resetLang();
      this.schemaService.removeStructuredData();
      this.schemaService.insertSchema(SchemaOrgWebpageDataTemplate);
      this.urlService.setPreviousUrl(event.url);
    });

    if (this.document.body.classList.contains('ssr')) {
      this.document.body.classList.remove('ssr');
    }
  }

  ngOnDestroy(): void {
    this.unsubscribe.next();
    this.unsubscribe.complete();
  }

  private setupAnalyticsTracking(): void {
    // Navigation end used to trigger gemius and gtag
    const navigationEnd$: Observable<NavigationEnd> = (this.router.events.pipe(filter((e) => e instanceof NavigationEnd)) as Observable<NavigationEnd>).pipe(
      tap((event: NavigationEnd) => {
        if (!this.isFirstNavigation) {
          pp_gemius_hit(environment.gemiusId, `page=${event.urlAfterRedirects}`);
        }
        // wrapped in a setTimeout because the router event fires before we can grab <title> from the page's <head>.
        setTimeout(() => {
          this.isFirstNavigation = false;
        }, 0);
      })
    );

    // Child activationEnd to get the leaf route data in order to see if we send pageViews there.
    (this.router.events.pipe(takeUntil(this.unsubscribe)).pipe(filter((e) => e instanceof ChildActivationEnd)) as Observable<ChildActivationEnd>)
      .pipe(
        // ChildActivationEnd triggers for every path activation, we need only the leaf so after navigation end
        // we get all of the childActivationEnd events and we only need the first one.
        buffer(navigationEnd$),
        map(([leafNode]: ChildActivationEnd[]) => (leafNode as ChildActivationEnd)?.snapshot?.firstChild?.data),
        map((data?: Data & { omitGlobalPageView?: boolean }) => !data?.omitGlobalPageView)
      )
      .subscribe((shouldSendGlobalAnalytics: boolean) => {
        console.log('Should send global analytics: ', shouldSendGlobalAnalytics);
        if (shouldSendGlobalAnalytics) {
          setTimeout(() => {
            this.analyticsService.sendPageView();
          }, 100);
        }
      });
  }

  private cleanup(): void {
    if (!this.storageService.getLocalStorageData(ADVERTICUM_COOKIE_CLEAN_KEY, false)) {
      this.storageService.removeCookie(ADVERTICUM_COOKIE);
      this.storageService.setLocalStorageData(ADVERTICUM_COOKIE_CLEAN_KEY, true);
    }
  }

  private setupMobileAppParameters(): void {
    if (!this.utilsService.isBrowser()) {
      this.checkAndCleanMobileElements();
    }
  }

  private checkAndCleanMobileElements(data: Record<string, any> = {}): void {
    let hasMobileAppHeader: RegExpMatchArray | null | undefined = null;
    if (this.req?.headers) {
      const userAgent: string = Object.entries(this.req.headers).find((value) => value?.[0] === 'user-agent')?.[1];
      hasMobileAppHeader = userAgent?.match(/mobile\/ios/);
    }

    const isMobileApp = !!data?.['isMobileApp'] || hasMobileAppHeader || !!this.route.snapshot.queryParams['forceMobileApp'];
    if (!isMobileApp) {
      return;
    }

    this.document.head.setAttribute('data-mobile-app', 'data-mobile-app');
    const skipMobileAppElements = Array.from(this.document.querySelectorAll(`[${MOBILE_APP_DOM_REMOVE_DATA_KEY}]`));
    skipMobileAppElements.forEach((element: any) => {
      element.remove();
    });
  }
}
