// http://detectmobilebrowsers.com/

const mobileAppRegex = /mobile\/(?<platform>.+)\/(?<device>.+)\/(?<os_version>.+)\/(?<app_version>.+)\/(?<device_language>.+)\/(?<userid>.+)/gi;

/**
 * Detects if the user agent is from a Facebook in-app browser
 */
export function isFacebookInAppBrowser(userAgent: string): boolean {
  if (!userAgent) {
    return false;
  }
  return userAgent.indexOf('FBAN') > -1 || userAgent.indexOf('FBAV') > -1;
}

/**
 * Detects if the user agent is from an Instagram in-app browser
 */
export function isInstagramInAppBrowser(userAgent: string): boolean {
  if (!userAgent) {
    return false;
  }
  return userAgent.indexOf('Instagram') > -1;
}

/**
 * Detects if the user agent is from any social media in-app browser
 */
export function isSocialMediaInAppBrowser(userAgent: string): boolean {
  return isFacebookInAppBrowser(userAgent) || isInstagramInAppBrowser(userAgent);
}

export function isMobileApp(userAgent: string): boolean {
  if (!userAgent) {
    return false;
  }

  // Exclude social media in-app browsers - they should show the header
  if (isSocialMediaInAppBrowser(userAgent)) {
    return false;
  }

  // Ha kellenek az adatok:
  // const [agent, platform, device, os_version, app_version, device_language, user_id] = mobileRegex.exec(userAgent) ?? []
  // const appData = { agent, platform, device, os_version, app_version, device_language, user_id }
  return !!userAgent.match(mobileAppRegex);
}

/* eslint-disable max-len */
const mobileDeviceRegex =
  /(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|miuibrowser|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series[46]0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i;

/* eslint-enable */

export function checkIsMobileDevice(userAgent: string): boolean {
  return mobileDeviceRegex.test(userAgent);
}
