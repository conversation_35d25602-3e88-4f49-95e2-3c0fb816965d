@use 'shared' as *;

.article-text-formatter {
  &:first-child {
    &.has-initial {
      > p:first-child:first-letter {
        display: inline-block;
        background: var(--kui-article-initial-color, var(--kui-blue-900));
        border: 1px solid var(--kui-article-initial-color, var(--kui-blue-900));
        font-family: var(--kui-font-secondary);
        color: var(--kui-blue-50);
        font-size: 56px;
        font-weight: 700;
        line-height: 44px;
        letter-spacing: 0;
        text-align: center;
        float: left;
        padding: 12px;
        margin: 8px 24px 12px 0;
      }
    }
  }
  p:first-child {
    margin-top: 0px;
  }

  &:last-child {
    margin-bottom: -24px;
  }

  p,
  li,
  td {
    font-size: 16px;
    font-weight: 400;
    font-style: normal;
    line-height: 24px;
    text-align: left;
    margin: 24px 0;
    overflow-wrap: break-word;

    a {
      color: var(--kui-blue-500);
      font-weight: 500;
    }
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    margin: 24px 0;
  }

  h1 {
    font-size: 32px;
    line-height: 40px;
  }

  h2 {
    font-size: 28px;
    line-height: 36px;
  }

  h3 {
    font-size: 24px;
    line-height: 32px;
  }

  h4 {
    font-size: 20px;
    line-height: 28px;
  }

  h5 {
    font-size: 16px;
    line-height: 24px;
  }

  ul {
    list-style-type: square;
    list-style-position: inside;
  }

  ol {
    list-style: decimal inside;

    li {
      position: relative;
      list-style-position: outside;
      margin-left: 20px !important;
      padding-left: 10px !important;

      &::marker {
        color: var(--kui-blue-500);
        font-size: 16px;
        font-weight: 800;
        line-height: 22px;
        text-align: center;
      }

      &:before {
        content: ' ';
        width: 26px;
        height: 26px;
        border: 1px solid var(--kui-blue-100);
        display: block;
        position: absolute;
        left: -26px;
        top: -4px;
      }
    }
  }

  ul,
  ol {
    padding-left: 18px;
    margin: 18px 0 18px;

    li {
      padding-left: 5px;
      display: list-item;
      margin: 8px 0;
      font-weight: 500;

      &::marker {
        color: var(--kui-blue-500);
        font-size: 18px;
      }

      color: var(--kui-slate-950);
    }
  }

  figure.table {
    max-width: 100%;
    overflow-x: auto;
  }

  table {
    margin: auto;

    td {
      padding: 12px 15px;
      border: 1px solid var(--kui-slate-500);
      font-size: 14px;
      color: var(--kui-slate-950);
    }
  }

  .article-text-table {
    display: block;
    overflow: auto;
    margin: 35px 0;

    table {
      width: calc(100% - 2px);

      tr {
        &:first-child {
          td {
            border: 0;
          }
        }

        td {
          padding: 12px 15px;
          border: 1px solid var(--kui-slate-500);
          font-size: 14px;
          color: var(--kui-slate-950);
        }
      }
    }
  }

  .image {
    display: table;
    clear: both;
    text-align: center;
    margin-inline: auto;

    &.image-style-align-left {
      float: left;
      margin-right: 20px;
      margin-bottom: unset;
      margin-top: 0;

      @include media-breakpoint-down(sm) {
        margin-right: 10px;
      }
    }

    &.image-style-align-right {
      float: right;
      margin-left: 20px;
      margin-bottom: unset;
      margin-top: 0;

      @include media-breakpoint-down(sm) {
        margin-left: 10px;
      }
    }

    figcaption {
      text-align: center;
      opacity: 0.5;
      font-style: italic;
      padding-top: 5px;
      font-size: 13px;
      word-break: break-word;
      display: table-caption;
      caption-side: bottom;
    }
  }

  .custom-text-style {
    display: block;
    clear: both;
  }

  .custom-text-style {
    display: block;

    &.quote {
      p {
        font-family: var(--kui-font-secondary);
        color: var(--kui-blue-900);
        font-size: 24px;
        font-weight: 700;
        line-height: 30px;
        position: relative;

        margin: 0;

        &:first-child:before {
          content: '„';
          position: absolute;
          top: -44px;
          font-size: 2em;
        }

        &:first-child:after {
          content: '”';
          position: absolute;
          bottom: -36px;
          font-size: 2em;
          right: 0;
        }

        &:first-child {
          margin-bottom: 44px;
          display: inline-block;
        }
      }

      margin-top: 56px;
      font-family: var(--kui-font-secondary);
      color: var(--kui-blue-900);
      font-size: 24px;
      font-weight: 700;
      line-height: 30px;
      position: relative;
      padding-bottom: 8px;

      @include media-breakpoint-down(sm) {
        margin: 26px 0;
      }
    }

    & + p {
      margin-top: 0;
    }

    &.highlight {
      p {
        position: relative;
        width: auto;
        white-space: pre-wrap;
        color: var(--kui-blue-900);

        font-size: 20px;
        font-weight: 700;
        line-height: 26px;
        letter-spacing: 0.015em;
      }

      margin: 25px 0;
      display: block;
      position: relative;
      width: auto;
      color: var(--kui-blue-900);
      white-space: pre-wrap;
      font-size: 20px;
      font-weight: 700;
      line-height: 26px;
      letter-spacing: 0.015em;
      text-align: left;
      border-left: 8px solid var(--kui-blue-900);
      border-radius: 2px;
      padding: 0 0 0 24px;

      @include media-breakpoint-down(sm) {
        margin-top: 26px;
        margin-bottom: 26px;
      }

      @media print {
        color: black;
        border: unset;
      }
    }

    &.border-text {
      margin: 25px 0;
      padding: 16px 24px 16px 19px;
      background-color: var(--kui-white);

      border-radius: 2px;
      border: 1px solid var(--kui-blue-900);
      border-left-width: 8px;

      display: flex;
      flex-direction: column;
      align-items: flex-start;
      gap: 4px;

      @include media-breakpoint-down(sm) {
        margin: 26px 0;
      }

      p {
        color: var(--kui-blue-900);
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 24px; /* 150% */
        padding: 0;
        margin: 0;
      }

      h2,
      h3,
      h4,
      h5,
      h6 {
        font-family: var(--kui-font-secondary);
        color: var(--kui-blue-900);

        font-size: 20px;
        font-style: normal;
        font-weight: 400;
        line-height: 26px; /* 130% */
        letter-spacing: 0.2px;
      }
    }
  }

  .raw-html-embed {
    // Do not use flex here, because some 3rd party stuff (iframe.ly) doesn't like it
    display: block;
    position: relative;
    overflow: hidden;

    > * :not(script) {
      margin: 0 auto;
      display: block;
    }

    iframe[data-src*='videa'] {
      @include media-breakpoint-down(sm) {
        max-width: 100%;
        height: calc(100vw * 9 / 16);
      }
    }
  }
}

.video-container {
  position: relative;
  padding-bottom: 56.25%;
  padding-top: 30px;
  height: 0;
  overflow: hidden;
  margin-top: 20px;
  margin-bottom: 20px;
}

.video-container iframe,
.video-container object,
.video-container embed {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
