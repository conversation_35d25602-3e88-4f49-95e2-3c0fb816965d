@use 'shared' as *;

:host {
  display: block;
  border: 1px solid var(--kui-blue-900);
  color: var(--kui-slate-950);
  width: 100%;
  .quiz {
    &-question {
      padding: 16px;
      &-image {
        position: static;
        aspect-ratio: 16/9;
        object-fit: cover;
        max-height: 400px;
        width: 100%;
        height: 100%;
      }
      &-text {
        font-size: 24px;
        font-weight: 700;
        line-height: 28px;
      }
    }
    &-stepper {
      font-size: 14px;
      line-height: 18px;
    }
    &-footer {
      padding: 16px;
    }
    &-button {
      border-radius: 4px;
      font-family: inherit;
      color: var(--kui-white);
      background-color: var(--kui-blue-900);
      padding: 12px 30px;
      text-align: center;
      font-weight: 700;
      width: 100%;
      &.disabled {
        opacity: 0.4;
        cursor: not-allowed;
      }
    }
    &-result {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 16px;
      @include media-breakpoint-down(md) {
        flex-direction: column;
      }
      &-share {
        display: flex;
        align-items: center;
        background-color: var(--kui-socialcolor-facebook);
        color: var(--kui-white);
        cursor: pointer;
        border-radius: 4px;
        padding: 10px 16px;
        gap: 8px;
        font-size: 16px;
        font-weight: 500;
        line-height: 20px;
        @include media-breakpoint-down(md) {
          justify-content: center;
          width: 100%;
        }
      }
      &-link {
        display: flex;
        align-items: center;
        gap: 6px;
        font-weight: 600;
        letter-spacing: 0.015em;
        color: var(--kui-blue-900);
        &:hover {
          color: var(--kui-blue-700);
        }
      }
    }
  }
  .answer-list {
    padding: 16px;
    display: flex;
    flex-direction: column;
    gap: 20px;
    &-item {
      cursor: pointer;
      padding: 10px 16px;
      border-radius: 4px;
      border: 1px solid var(--kui-gray-200);
      background-color: var(--kui-gray-50);
      display: flex;
      align-items: center;
      position: relative;
      &:not(.selected):hover {
        border: 1px solid var(--kui-blue-900);
      }
      .result-label {
        display: none;
        margin-left: auto;
        font-size: 16px;
        font-weight: 500;
        line-height: 20px;
        color: var(--kui-white);
      }
      .radio-input {
        position: absolute;
        pointer-events: none;
        opacity: 0;
      }
      .radio-input:checked + label {
        &:after {
          opacity: 1;
        }
      }
      .radio-label {
        display: flex;
        align-items: center;
        font-size: 16px;
        font-weight: 500;
        line-height: 20px;
        margin-right: 10px;
        &.hidden-original-circle {
          background-color: transparent;
          &:before {
            content: none;
          }
          .extra-label {
            left: 16px;
          }
        }
        &:before {
          display: inline-block;
          content: '';
          min-width: 20px;
          height: 20px;
          border-radius: 50%;
          margin-right: 12px;
          border: 2px solid var(--kui-gray-800);
          background-color: var(--kui-white);
        }
        .extra-label {
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          opacity: 0;
          font-weight: 700;
          font-size: 12px;
          line-height: 24px;
          display: flex;
          justify-content: center;
        }
      }
      &.correct {
        background: var(--kui-success);
      }
      &.wrong {
        background: var(--kui-danger);
      }
      &.wrong,
      &.correct {
        .result-label {
          display: block;
        }
        .radio-label {
          color: var(--kui-white);
          margin-left: 32px;
          .extra-label {
            &.correct {
              opacity: 0;
              transition-duration: 0.3s;
            }
            &.wrong {
              opacity: 1;
              transition-duration: 0.3s;
            }
          }
        }
      }
    }
  }
}
