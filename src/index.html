<!doctype html>
<html lang="hu">
  <head>
    <base href="/" />
    <title>Ma<PERSON><PERSON></title>
    <meta charset="utf-8" />
    <meta content="index, follow, max-image-preview:large" name="robots" />
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=5.0" name="viewport" />
    <link href="/favicon.png" rel="icon" type="image/png" />
    <link href="/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180" />
    <link href="/favicon-32x32.png" rel="icon" sizes="32x32" type="image/png" />
    <link href="/favicon-16x16.png" rel="icon" sizes="16x16" type="image/png" />
    <link href="/manifest.json" rel="manifest" />
    <link color="#143c6d" href="/safari-pinned-tab.svg" rel="mask-icon" />
    <meta content="#143c6d" name="msapplication-TileColor" />
    <meta content="#143c6d" name="theme-color" />
    <link href="https://fonts.gstatic.com" rel="preconnect" />
    <link href="https://magyarnemzet.hu/publicapi/hu/rss/magyar_nemzet/articles" rel="alternate" title="Magyar Nemzet Hírek" type="application/rss+xml" />

    <script class="structured-data" type="application/ld+json"></script>

    <script>
      if (document.head.dataset.mobileApp || window.location.search.match(/force-mobile=1/)) {
        console.log('[mobileApp] identified - removing QC...');
        const skipMobileAppElements = Array.from(document.querySelectorAll('[data-skip-on-mobile-app]'));
        skipMobileAppElements.forEach((element) => element.remove());
      }
    </script>
    <script data-skip-on-mobile-app src="/assets/scripts/inmobi.js"></script>
    <!-- InMobi Choice. Consent Manager Tag v3.0 (for TCF 2.2) -->
    <script async data-skip-on-mobile-app type="text/javascript">
      // Make sure we call gemius init only after InMobi is loaded and inited
      console.log(' >> initializing InMobi ready callback for AdOcean');
      const inMobiReadyCallback = () => {
        console.log(' >> InMobi ready');

        if (!initAdOcean) {
          console.warn(' >> << no adocean init found');
        }
        !adOceanInited && initAdOcean && initAdOcean();
      };

      InMobiHandler.init('gq2uc_c-uMyQL', 'magyarnemzet.hu', inMobiReadyCallback);
    </script>
    <!-- End InMobi Choice. Consent Manager Tag v3.0 (for TCF 2.2) -->

    <script>
      window.adocf = {
        useDOMContentLoaded: true,
      };

      window.PRE_adocean_queue = [];
      window.adOceanInited = false;

      window.adoQueueFn = (fn, args) => {
        PRE_adocean_queue.push({ fn, args });
      };
    </script>

    <!-- AD OCEAN GEMIUS -->
    <script data-skip-on-mobile-app src="https://hu.adocean.pl/files/js/ado.js" type="text/javascript"></script>

    <script async data-skip-on-mobile-app type="text/javascript">
      /* (c)AdOcean 2003-2020 */

      window.initAdOcean = () => {
        console.log(' >> init AdOcean');
        if (typeof window.ado !== 'object') {
          window.ado = {};
          window.ado.config = window.ado.preview = window.ado.placement = window.ado.master = window.ado.slave = function () {};
        }

        window.ado.config({
          mode: 'new',
          xml: false,
          consent: true,
          characterEncoding: true,
          attachReferrer: true,
          fpc: 'auto',
          defaultServer: 'hu.adocean.pl',
          cookieDomain: 'SLD',
        });

        window.ado.preview({ enabled: true });

        window.adOceanInited = true;
        window.PRE_adocean_queue.forEach(({ fn, args }) => {
          window.ado[fn](...args);
        });
      };

      window.setTimeout(() => {
        if (!window.adOceanInited && window.Ado) {
          console.warn(' >> GFC initialization timeout: activating AdOcean');
          window.initAdOcean();
        } else if (!window.Ado) {
          console.error(' <!> GFC initialization timeout: AdOcean is not loaded! Aborting');
        }
      }, 30000);
    </script>

    <!-- AD OCEAN GEMIUS -->
  </head>

  <body>
    <div class="trendency-fullscreen-loader" id="init-loader"></div>

    <app-root></app-root>
    <script type="text/javascript">
      function isSocialMediaInAppBrowser(userAgent) {
        // Facebook in-app browser
        if (userAgent.indexOf('FBAN') > -1 || userAgent.indexOf('FBAV') > -1) {
          return true;
        }
        return false;
      }

      function isMobileAppCheck(userAgent) {
        const mobileAppRegex = /mobile\/(?<platform>.+)\/(?<device>.+)\/(?<os_version>.+)\/(?<app_version>.+)\/(?<device_language>.+)\/(?<userid>.+)/gi;

        if (isSocialMediaInAppBrowser(userAgent)) {
          return false;
        }

        return !!userAgent.match(mobileAppRegex);
      }

      const toHideOnMobileApp = ['header', 'footer'];
      const isMobileApp = isMobileAppCheck(navigator.userAgent || navigator.vendor || window.opera);

      (function () {
        if (!isMobileApp) {
          return;
        }
        console.log('[isMobileApp] Hiding header and footer...');
        toHideOnMobileApp.forEach((selector) => {
          Array.from(document.querySelectorAll(selector)).forEach((element) => {
            element.style.display = 'none';
          });
        });
      })();
    </script>

    <script src="/assets/scripts/init-loader.js"></script>
    <script async defer src="/assets/scripts/version.js"></script>
  </body>
  <!-- Google Tag Manager (noscript) -->
  <noscript>
    <iframe src="https://www.googletagmanager.com/ns.html?id=GTM-WCLP7HF" height="0" width="0" style="display: none; visibility: hidden"></iframe>
  </noscript>
  <!-- End Google Tag Manager (noscript) -->
</html>
