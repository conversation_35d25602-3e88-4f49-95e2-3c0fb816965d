<!DOCTYPE html>
<html>
<head>
    <title>User Agent Debug</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
</head>
<body>
    <h1>User Agent Debug</h1>
    <div id="results"></div>
    
    <script>
        // Current mobile app detection logic from your codebase
        function isMobileAppCheck(userAgent) {
            const mobileAppRegex = /mobile\/(?<platform>.+)\/(?<device>.+)\/(?<os_version>.+)\/(?<app_version>.+)\/(?<device_language>.+)\/(?<userid>.+)/gi;
            return !!userAgent.match(mobileAppRegex);
        }
        
        // Facebook in-app browser detection
        function isFacebookApp(userAgent) {
            return (userAgent.indexOf("FBAN") > -1) || (userAgent.indexOf("FBAV") > -1);
        }
        
        // Get user agent
        const ua = navigator.userAgent || navigator.vendor || window.opera;
        
        // Display results
        const results = document.getElementById('results');
        results.innerHTML = `
            <h2>Current User Agent:</h2>
            <p style="word-break: break-all; background: #f0f0f0; padding: 10px;">${ua}</p>
            
            <h2>Detection Results:</h2>
            <p><strong>Is Mobile App (current logic):</strong> ${isMobileAppCheck(ua)}</p>
            <p><strong>Is Facebook In-App Browser:</strong> ${isFacebookApp(ua)}</p>
            
            <h2>Analysis:</h2>
            <p><strong>Contains FBAN:</strong> ${ua.indexOf("FBAN") > -1}</p>
            <p><strong>Contains FBAV:</strong> ${ua.indexOf("FBAV") > -1}</p>
            <p><strong>Contains "mobile/":</strong> ${ua.toLowerCase().indexOf("mobile/") > -1}</p>
            
            <h2>Header Should Show:</h2>
            <p style="font-size: 20px; color: ${!isMobileAppCheck(ua) ? 'green' : 'red'};">
                ${!isMobileAppCheck(ua) ? 'YES - Header should be visible' : 'NO - Header will be hidden'}
            </p>
        `;
    </script>
</body>
</html>
